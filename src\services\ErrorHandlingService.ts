import { Alert } from 'react-native';
import * as Haptics from 'expo-haptics';

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
  onRetry?: (attempt: number, error: Error) => void;
  onMaxRetriesReached?: (error: Error) => void;
}

export interface ErrorContext {
  operation: string;
  userFriendlyName: string;
  criticalLevel: 'low' | 'medium' | 'high';
  fallbackAvailable: boolean;
}

export interface EnhancedError extends Error {
  code?: string;
  statusCode?: number;
  retryable?: boolean;
  userMessage?: string;
  context?: ErrorContext;
}

class ErrorHandlingService {
  private static instance: ErrorHandlingService;

  public static getInstance(): ErrorHandlingService {
    if (!ErrorHandlingService.instance) {
      ErrorHandlingService.instance = new ErrorHandlingService();
    }
    return ErrorHandlingService.instance;
  }

  // Default retry configurations for different operations
  private defaultConfigs: { [key: string]: RetryConfig } = {
    mealScan: {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 8000,
      backoffMultiplier: 2,
      retryableErrors: [
        'JSON Parse Failure',
        'invalid JSON format',
        'CRITICAL: AI returned invalid JSON',
        'Network request failed',
        'timeout',
        'MAX_TOKENS'
      ]
    },
    recipeGeneration: {
      maxRetries: 3,
      baseDelay: 1500,
      maxDelay: 10000,
      backoffMultiplier: 2,
      retryableErrors: [
        'JSON Parse Failure',
        'invalid JSON response',
        'Network request failed',
        'timeout',
        'MAX_TOKENS'
      ]
    },
    mealPlanGeneration: {
      maxRetries: 4,
      baseDelay: 2000,
      maxDelay: 15000,
      backoffMultiplier: 2,
      retryableErrors: [
        'JSON Parse Failure',
        'invalid JSON response',
        'Unable to generate meal plan',
        'Network request failed',
        'timeout',
        'MAX_TOKENS'
      ]
    },
    nutritionQuestion: {
      maxRetries: 2,
      baseDelay: 1000,
      maxDelay: 5000,
      backoffMultiplier: 1.5,
      retryableErrors: [
        'Network request failed',
        'timeout',
        'Gemini API error'
      ]
    }
  };

  // Enhanced retry function with exponential backoff and better error handling
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    operationType: string,
    context: ErrorContext,
    customConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.defaultConfigs[operationType], ...customConfig };
    let lastError: Error;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          // Show retry feedback to user
          this.showRetryFeedback(context.userFriendlyName, attempt, config.maxRetries);
          
          // Calculate delay with exponential backoff and jitter
          const delay = Math.min(
            config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
            config.maxDelay
          );
          const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
          
          await this.delay(delay + jitter);
        }

        const result = await operation();
        
        if (attempt > 0) {
          // Success after retry - show success feedback
          this.showSuccessFeedback(context.userFriendlyName);
        }
        
        return result;
      } catch (error) {
        lastError = error as Error;
        
        // Enhanced error analysis
        const enhancedError = this.enhanceError(lastError, context);
        
        // Check if error is retryable
        if (!this.isRetryableError(enhancedError, config.retryableErrors)) {
          throw enhancedError;
        }

        // Call retry callback if provided
        if (config.onRetry) {
          config.onRetry(attempt + 1, enhancedError);
        }

        // If this was the last attempt, handle max retries reached
        if (attempt === config.maxRetries) {
          if (config.onMaxRetriesReached) {
            config.onMaxRetriesReached(enhancedError);
          }
          
          this.showMaxRetriesReachedFeedback(context);
          throw this.createUserFriendlyError(enhancedError, context);
        }

        console.warn(`⚠️ ${context.operation} attempt ${attempt + 1} failed:`, enhancedError.message);
      }
    }

    throw lastError!;
  }

  // Enhanced error analysis and classification
  private enhanceError(error: Error, context: ErrorContext): EnhancedError {
    const enhanced: EnhancedError = {
      ...error,
      context
    };

    // Analyze error type and add metadata
    if (error.message.includes('Network request failed') || error.message.includes('fetch')) {
      enhanced.code = 'NETWORK_ERROR';
      enhanced.retryable = true;
      enhanced.userMessage = 'Network connection issue. Please check your internet connection.';
    } else if (error.message.includes('timeout')) {
      enhanced.code = 'TIMEOUT_ERROR';
      enhanced.retryable = true;
      enhanced.userMessage = 'Request timed out. The server is taking too long to respond.';
    } else if (error.message.includes('JSON Parse Failure') || error.message.includes('invalid JSON')) {
      enhanced.code = 'PARSE_ERROR';
      enhanced.retryable = true;
      enhanced.userMessage = 'Received invalid response format. This usually resolves on retry.';
    } else if (error.message.includes('MAX_TOKENS')) {
      enhanced.code = 'TOKEN_LIMIT_ERROR';
      enhanced.retryable = true;
      enhanced.userMessage = 'Response was too long and got cut off. Trying with a shorter request.';
    } else if (error.message.includes('API error: 429')) {
      enhanced.code = 'RATE_LIMIT_ERROR';
      enhanced.retryable = true;
      enhanced.userMessage = 'Too many requests. Please wait a moment before trying again.';
    } else if (error.message.includes('API error: 500') || error.message.includes('API error: 502') || error.message.includes('API error: 503')) {
      enhanced.code = 'SERVER_ERROR';
      enhanced.retryable = true;
      enhanced.userMessage = 'Server is temporarily unavailable. Retrying...';
    } else {
      enhanced.code = 'UNKNOWN_ERROR';
      enhanced.retryable = false;
      enhanced.userMessage = 'An unexpected error occurred. Please try again later.';
    }

    return enhanced;
  }

  // Check if an error is retryable based on configuration
  private isRetryableError(error: EnhancedError, retryableErrors: string[]): boolean {
    if (error.retryable === false) return false;

    // Defensive check for undefined/null retryableErrors
    if (!retryableErrors || !Array.isArray(retryableErrors)) {
      return false;
    }

    return retryableErrors.some(retryableError =>
      retryableError && error.message && error.message.includes(retryableError)
    );
  }

  // Create user-friendly error messages
  private createUserFriendlyError(error: EnhancedError, context: ErrorContext): EnhancedError {
    let userMessage = error.userMessage || 'An unexpected error occurred.';
    
    // Add context-specific messaging
    switch (context.operation) {
      case 'mealScan':
        userMessage = `Unable to analyze your meal photo after multiple attempts. ${userMessage}`;
        break;
      case 'recipeGeneration':
        userMessage = `Recipe generation failed after several tries. ${userMessage}`;
        break;
      case 'mealPlanGeneration':
        userMessage = `Meal plan creation encountered issues. ${userMessage}`;
        break;
      case 'nutritionQuestion':
        userMessage = `Unable to get nutrition advice right now. ${userMessage}`;
        break;
    }

    if (context.fallbackAvailable) {
      userMessage += ' A simplified version may be available.';
    }

    return {
      ...error,
      userMessage
    };
  }

  // User feedback methods
  private showRetryFeedback(operationName: string, attempt: number, maxRetries: number) {
    console.log(`🔄 Retrying ${operationName} (attempt ${attempt}/${maxRetries})`);
    
    // Haptic feedback for retry
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }

  private showSuccessFeedback(operationName: string) {
    console.log(`✅ ${operationName} succeeded after retry`);
    
    // Success haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }

  private showMaxRetriesReachedFeedback(context: ErrorContext) {
    console.error(`❌ ${context.operation} failed after all retry attempts`);
    
    // Error haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    
    // Show user alert for critical operations
    if (context.criticalLevel === 'high') {
      Alert.alert(
        'Operation Failed',
        `${context.userFriendlyName} couldn't be completed after multiple attempts. Please try again later.`,
        [{ text: 'OK', style: 'default' }]
      );
    }
  }

  // Utility delay function
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get network troubleshooting suggestions
  getNetworkTroubleshootingTips(): string[] {
    return [
      'Check if you have an active internet connection',
      'Try switching between WiFi and mobile data',
      'Restart your device if the problem persists',
      'Check if other apps can access the internet',
      'Ensure you\'re not behind a restrictive firewall',
      'Try moving to a location with better signal strength',
      'Clear the app cache and restart the app',
      'Check if your device\'s date and time are correct'
    ];
  }

  // Get specific error guidance based on error type
  getErrorGuidance(error: Error): string {
    const message = error.message.toLowerCase();

    if (message.includes('network request failed')) {
      return 'This appears to be a network connectivity issue. Please check your internet connection.';
    } else if (message.includes('timeout')) {
      return 'The request timed out. This could be due to slow internet or server issues.';
    } else if (message.includes('json') || message.includes('parse')) {
      return 'Received an invalid response from the server. This usually resolves on retry.';
    } else if (message.includes('401') || message.includes('unauthorized')) {
      return 'Authentication issue. Please restart the app.';
    } else if (message.includes('429') || message.includes('rate limit')) {
      return 'Too many requests. Please wait a moment before trying again.';
    } else if (message.includes('500') || message.includes('502') || message.includes('503')) {
      return 'Server is temporarily unavailable. Please try again later.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  // Enhanced network connectivity check with multiple fallbacks
  async checkNetworkConnectivity(): Promise<boolean> {
    const testEndpoints = [
      // Primary: Test our actual API endpoint first
      {
        url: `https://generativelanguage.googleapis.com/v1beta/models?key=${process.env.GEMINI_API_KEY || 'AIzaSyB3bzVudVDXpKaZHCeMOOhpGQRe54YFyr0'}`,
        method: 'GET' as const,
        timeout: 8000
      },
      // Fallback 1: Unsplash API
      {
        url: 'https://api.unsplash.com',
        method: 'GET' as const,
        timeout: 6000
      },
      // Fallback 2: Cloudflare DNS (very reliable)
      {
        url: 'https://*******',
        method: 'GET' as const,
        timeout: 5000
      },
      // Fallback 3: Google (if not blocked)
      {
        url: 'https://www.google.com',
        method: 'GET' as const,
        timeout: 5000
      }
    ];

    for (const endpoint of testEndpoints) {
      try {
        console.log(`🔍 Testing connectivity to ${endpoint.url}...`);
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), endpoint.timeout);

        const response = await fetch(endpoint.url, {
          method: endpoint.method,
          signal: controller.signal,
          headers: {
            'Accept': 'application/json',
          }
        });

        clearTimeout(timeoutId);

        if (response.ok || response.status < 500) {
          console.log(`✅ Network connectivity confirmed via ${endpoint.url} (Status: ${response.status})`);
          return true;
        }
      } catch (error) {
        console.warn(`⚠️ Connectivity test failed for ${endpoint.url}:`, error instanceof Error ? error.message : 'Unknown error');
        continue; // Try next endpoint
      }
    }

    console.error('❌ All network connectivity tests failed');
    return false;
  }

  // Enhanced error reporting for debugging
  reportError(error: EnhancedError, additionalContext?: any) {
    const errorReport = {
      timestamp: new Date().toISOString(),
      error: {
        message: error.message,
        code: error.code,
        stack: error.stack,
        userMessage: error.userMessage
      },
      context: error.context,
      additionalContext
    };

    console.error('🚨 Enhanced Error Report:', JSON.stringify(errorReport, null, 2));
    
    // In production, this could send to error tracking service
    // Example: Sentry.captureException(error, { extra: errorReport });
  }

  // Graceful degradation helper
  async withGracefulDegradation<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T> | T,
    context: ErrorContext
  ): Promise<T> {
    try {
      return await this.retryWithBackoff(primaryOperation, context.operation, context);
    } catch (primaryError) {
      console.warn(`⚠️ Primary operation failed, attempting fallback for ${context.operation}`);
      
      try {
        const result = await fallbackOperation();
        console.log(`✅ Fallback successful for ${context.operation}`);
        return result;
      } catch (fallbackError) {
        console.error(`❌ Both primary and fallback failed for ${context.operation}`);
        throw this.createUserFriendlyError(primaryError as EnhancedError, context);
      }
    }
  }
}

export default ErrorHandlingService.getInstance();
