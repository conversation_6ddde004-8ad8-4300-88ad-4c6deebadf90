import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Alert } from 'react-native';
import { Colors } from '../constants/Colors';
import ErrorHandlingService from '../services/ErrorHandlingService';
import ApiService from '../services/ApiService';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: string;
}

const NetworkDebugger: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (name: string, status: 'pending' | 'success' | 'error', message: string, details?: string) => {
    setTests(prev => {
      const existing = prev.find(t => t.name === name);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.details = details;
        return [...prev];
      } else {
        return [...prev, { name, status, message, details }];
      }
    });
  };

  const runNetworkTests = async () => {
    setIsRunning(true);
    setTests([]);

    try {
      // Test 1: Basic connectivity check
      updateTest('Basic Connectivity', 'pending', 'Testing...');
      try {
        const isConnected = await ErrorHandlingService.checkNetworkConnectivity();
        updateTest('Basic Connectivity', isConnected ? 'success' : 'error', 
          isConnected ? 'Connected' : 'Failed to connect');
      } catch (error) {
        updateTest('Basic Connectivity', 'error', 'Failed', error instanceof Error ? error.message : 'Unknown error');
      }

      // Test 2: Gemini API connectivity
      updateTest('Gemini API', 'pending', 'Testing...');
      try {
        const apiWorking = await ApiService.testApiConnectivity();
        updateTest('Gemini API', apiWorking ? 'success' : 'error', 
          apiWorking ? 'API accessible' : 'API not accessible');
      } catch (error) {
        updateTest('Gemini API', 'error', 'Failed', error instanceof Error ? error.message : 'Unknown error');
      }

      // Test 3: Simple fetch test
      updateTest('Direct Fetch Test', 'pending', 'Testing...');
      try {
        const response = await fetch('https://www.google.com', { method: 'HEAD' });
        updateTest('Direct Fetch Test', response.ok ? 'success' : 'error', 
          `Status: ${response.status} ${response.statusText}`);
      } catch (error) {
        updateTest('Direct Fetch Test', 'error', 'Failed', error instanceof Error ? error.message : 'Unknown error');
      }

      // Test 4: Unsplash API test
      updateTest('Unsplash API', 'pending', 'Testing...');
      try {
        const response = await fetch('https://api.unsplash.com/search/photos?query=food&per_page=1&client_id=QPySZeLMRd2Rw0BKoNKpXFwrHY0aSVZMwxvTmZaIZEs');
        updateTest('Unsplash API', response.ok ? 'success' : 'error', 
          `Status: ${response.status} ${response.statusText}`);
      } catch (error) {
        updateTest('Unsplash API', 'error', 'Failed', error instanceof Error ? error.message : 'Unknown error');
      }

      // Test 5: Actual API call test
      updateTest('Real API Call', 'pending', 'Testing...');
      try {
        const response = await ApiService.askNutritionQuestion('What is protein?');
        updateTest('Real API Call', 'success', 'API call successful');
      } catch (error) {
        updateTest('Real API Call', 'error', 'Failed', error instanceof Error ? error.message : 'Unknown error');
      }

    } catch (error) {
      Alert.alert('Test Error', error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success': return Colors.success;
      case 'error': return Colors.error;
      case 'pending': return Colors.warning;
      default: return Colors.text;
    }
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Network Diagnostics</Text>
      
      <TouchableOpacity 
        style={[styles.button, isRunning && styles.buttonDisabled]} 
        onPress={runNetworkTests}
        disabled={isRunning}
      >
        <Text style={styles.buttonText}>
          {isRunning ? 'Running Tests...' : 'Run Network Tests'}
        </Text>
      </TouchableOpacity>

      <ScrollView style={styles.resultsContainer}>
        {tests.map((test, index) => (
          <View key={index} style={styles.testResult}>
            <View style={styles.testHeader}>
              <Text style={styles.testIcon}>{getStatusIcon(test.status)}</Text>
              <Text style={styles.testName}>{test.name}</Text>
            </View>
            <Text style={[styles.testMessage, { color: getStatusColor(test.status) }]}>
              {test.message}
            </Text>
            {test.details && (
              <Text style={styles.testDetails}>{test.details}</Text>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: Colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: Colors.primary,
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: Colors.gray400,
  },
  buttonText: {
    color: Colors.white,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsContainer: {
    flex: 1,
  },
  testResult: {
    backgroundColor: Colors.white,
    padding: 15,
    marginBottom: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  testHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  testIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  testName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    flex: 1,
  },
  testMessage: {
    fontSize: 14,
    marginBottom: 5,
  },
  testDetails: {
    fontSize: 12,
    color: Colors.textLight,
    fontStyle: 'italic',
  },
});

export default NetworkDebugger;
