// Network debugging script for Nutri AI app
// Run this with: node debug-network.js

const GEMINI_API_KEY = 'AIzaSyB3bzVudVDXpKaZHCeMOOhpGQRe54YFyr0';
const GEMINI_TEXT_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent';
const UNSPLASH_ACCESS_KEY = '*******************************************';
const UNSPLASH_API_URL = 'https://api.unsplash.com';

async function testConnectivity() {
  console.log('🔍 Starting network connectivity tests...\n');

  // Test 1: Basic connectivity
  console.log('1. Testing basic connectivity...');
  const basicTests = [
    'https://www.google.com',
    'https://*******',
    'https://api.unsplash.com',
    'https://generativelanguage.googleapis.com'
  ];

  for (const url of basicTests) {
    try {
      const response = await fetch(url, { 
        method: 'HEAD',
        timeout: 5000 
      });
      console.log(`✅ ${url}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`❌ ${url}: ${error.message}`);
    }
  }

  console.log('\n2. Testing Gemini API...');
  try {
    const response = await fetch(`${GEMINI_TEXT_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: "test"
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: 5,
        }
      })
    });

    console.log(`✅ Gemini API: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Gemini API response received successfully');
    } else {
      const errorText = await response.text();
      console.log(`❌ Gemini API error: ${errorText.substring(0, 200)}`);
    }
  } catch (error) {
    console.log(`❌ Gemini API: ${error.message}`);
  }

  console.log('\n3. Testing Unsplash API...');
  try {
    const response = await fetch(`${UNSPLASH_API_URL}/search/photos?query=food&per_page=1&client_id=${UNSPLASH_ACCESS_KEY}`);
    console.log(`✅ Unsplash API: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Unsplash API returned ${result.results?.length || 0} results`);
    } else {
      const errorText = await response.text();
      console.log(`❌ Unsplash API error: ${errorText.substring(0, 200)}`);
    }
  } catch (error) {
    console.log(`❌ Unsplash API: ${error.message}`);
  }

  console.log('\n4. Network environment info...');
  console.log(`Node.js version: ${process.version}`);
  console.log(`Platform: ${process.platform}`);
  console.log(`Architecture: ${process.arch}`);
  
  // Check if we're behind a proxy
  console.log(`HTTP_PROXY: ${process.env.HTTP_PROXY || 'Not set'}`);
  console.log(`HTTPS_PROXY: ${process.env.HTTPS_PROXY || 'Not set'}`);
  console.log(`NO_PROXY: ${process.env.NO_PROXY || 'Not set'}`);

  console.log('\n✅ Network diagnostic complete!');
}

// Add timeout to fetch if not available
if (!global.fetch) {
  console.log('❌ Fetch not available in this Node.js version');
  process.exit(1);
}

testConnectivity().catch(error => {
  console.error('❌ Diagnostic script failed:', error);
  process.exit(1);
});
